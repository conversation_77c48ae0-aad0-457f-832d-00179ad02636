{% extends "base.html" %}
{% block title %}Leaderboard - ITian Club{% endblock %}

{% block content %}
<div class="leaderboard-container">
    <!-- Enhanced Header -->
    <div class="leaderboard-header glass-card fade-in-up mb-4">
        <div class="header-content">
            <div class="header-icon">
                <div class="trophy-container">
                    <i class="fas fa-trophy"></i>
                    <div class="trophy-glow"></div>
                </div>
            </div>
            <div class="header-text">
                <h1 class="leaderboard-title mb-2">
                    Leaderboard
                </h1>
                <p class="leaderboard-subtitle">See how you rank among your peers in the ITian Club Aptitude Quiz</p>
                <div class="header-stats">
                    <span class="stat-badge">
                        <i class="fas fa-users me-1"></i>
                        <span id="liveParticipants">0</span> Active
                    </span>
                    <span class="stat-badge">
                        <i class="fas fa-clock me-1"></i>
                        Live Results
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Stats Overview -->
    <div class="stats-overview glass-card reveal mb-4">
        <div class="stats-header mb-4">
            <h3 class="stats-title">
                <i class="fas fa-chart-line me-2"></i>
                Competition Statistics
            </h3>
        </div>
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="enhanced-stat-card">
                    <div class="stat-icon-container">
                        <div class="stat-icon users-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-pulse"></div>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" id="totalParticipants">0</h4>
                        <p class="stat-label">Total Participants</p>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up text-success me-1"></i>
                            <span class="trend-text">+12% this week</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="enhanced-stat-card">
                    <div class="stat-icon-container">
                        <div class="stat-icon score-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-pulse"></div>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" id="avgScore">0</h4>
                        <p class="stat-label">Average Score</p>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up text-success me-1"></i>
                            <span class="trend-text">+5.2% improvement</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="enhanced-stat-card">
                    <div class="stat-icon-container">
                        <div class="stat-icon crown-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="stat-pulse"></div>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" id="topScore">0</h4>
                        <p class="stat-label">Top Score</p>
                        <div class="stat-trend">
                            <i class="fas fa-trophy text-warning me-1"></i>
                            <span class="trend-text">New record!</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="enhanced-stat-card">
                    <div class="stat-icon-container">
                        <div class="stat-pulse"></div>
                    </div>
                    <div class="stat-content">
                        <h4 class="stat-number" id="lastUpdated">Live</h4>
                        <p class="stat-label">Last Updated</p>
                        <div class="stat-trend">
                            <i class="fas fa-sync text-info me-1"></i>
                            <span class="trend-text">Real-time</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Leaderboard Table -->
    <div class="leaderboard-table-container glass-card reveal">
        <div class="table-header mb-4">
            <h3 class="table-title">
                <i class="fas fa-ranking-star me-2"></i>
                Competition Rankings
            </h3>
            <div class="table-filters">
                <button class="filter-btn active" data-filter="all">
                    <i class="fas fa-list me-1"></i>All
                </button>
                <button class="filter-btn" data-filter="top10">
                    <i class="fas fa-crown me-1"></i>Top 10
                </button>
                <button class="filter-btn" data-filter="recent">
                    <i class="fas fa-clock me-1"></i>Recent
                </button>
            </div>
        </div>

        <div class="table-responsive">
            <table class="enhanced-leaderboard-table">
                <thead>
                    <tr>
                        <th class="rank-header">
                            <div class="header-content">
                                <i class="fas fa-medal me-2"></i>
                                <span>Rank</span>
                            </div>
                        </th>
                        <th class="profile-header">
                            <div class="header-content">
                                <i class="fas fa-user me-2"></i>
                                <span>Profile</span>
                            </div>
                        </th>
                        <th class="name-header">
                            <div class="header-content">
                                <i class="fas fa-id-card me-2"></i>
                                <span>Name</span>
                            </div>
                        </th>
                        <th class="score-header sortable" data-sort="score">
                            <div class="header-content">
                                <i class="fas fa-star me-2"></i>
                                <span>Total Score</span>
                                <i class="fas fa-sort sort-icon"></i>
                            </div>
                        </th>
                        <th class="category-header sortable" data-sort="math">
                            <div class="header-content">
                                <i class="fas fa-calculator me-2"></i>
                                <span>Math</span>
                                <i class="fas fa-sort sort-icon"></i>
                            </div>
                        </th>
                        <th class="category-header sortable" data-sort="reasoning">
                            <div class="header-content">
                                <i class="fas fa-brain me-2"></i>
                                <span>Reasoning</span>
                                <i class="fas fa-sort sort-icon"></i>
                            </div>
                        </th>
                        <th class="category-header sortable" data-sort="verbal">
                            <div class="header-content">
                                <i class="fas fa-language me-2"></i>
                                <span>Verbal</span>
                                <i class="fas fa-sort sort-icon"></i>
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody id="leaderboard-body">
                    <!-- Filled dynamically via JS -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Loading State -->
    <div id="loadingState" class="glass-card text-center" style="display: none;">
        <div class="loading-spinner mb-3">
            <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
        </div>
        <p>Loading leaderboard data...</p>
    </div>

    <!-- No Data State -->
    <div id="noDataState" class="glass-card text-center" style="display: none;">
        <div class="no-data-icon mb-3">
            <i class="fas fa-chart-bar fa-3x text-muted"></i>
        </div>
        <h4>No Data Available</h4>
        <p class="text-muted">No participants have completed the quiz yet.</p>
    </div>
</div>

<style>
    /* Enhanced Leaderboard Container */
    .leaderboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: var(--space-lg);
    }

    /* Enhanced Header */
    .leaderboard-header {
        background: var(--bg-glass);
        backdrop-filter: blur(30px);
        -webkit-backdrop-filter: blur(30px);
        border: 1px solid var(--border-secondary);
        box-shadow: var(--shadow-2xl);
        position: relative;
        overflow: hidden;
    }

    .leaderboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--accent-yellow), var(--primary-blue), var(--primary-purple));
    }

    .header-content {
        display: flex;
        align-items: center;
        gap: var(--space-2xl);
    }

    .header-icon {
        position: relative;
    }

    .trophy-container {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--accent-yellow), #fbbf24);
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-dark);
        font-size: var(--font-size-3xl);
        box-shadow: var(--shadow-xl);
        position: relative;
        animation: trophy-glow 2s ease-in-out infinite;
    }

    @keyframes trophy-glow {

        0%,
        100% {
            box-shadow: var(--shadow-xl);
        }

        50% {
            box-shadow: 0 0 30px rgba(251, 191, 36, 0.6);
        }
    }

    .trophy-glow {
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        background: radial-gradient(circle, rgba(251, 191, 36, 0.2), transparent);
        border-radius: var(--radius-2xl);
        animation: pulse 2s infinite;
    }

    .leaderboard-title {
        font-size: var(--font-size-4xl);
        font-weight: 800;
        color: var(--text-primary);
        margin: 0;
    }

    .leaderboard-subtitle {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
        margin: 0;
    }

    .header-stats {
        display: flex;
        gap: var(--space-lg);
        margin-top: var(--space-md);
    }

    .stat-badge {
        display: inline-flex;
        align-items: center;
        padding: var(--space-sm) var(--space-md);
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-full);
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        font-weight: 500;
        backdrop-filter: blur(10px);
    }

    .stat-card p {
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0;
        font-weight: 500;
    }

    .leaderboard-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .leaderboard-table th {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-weight: 600;
        padding: 1rem;
        text-align: left;
        border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .leaderboard-table td {
        padding: 1rem;
        color: white;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
    }

    .leaderboard-table tbody tr {
        transition: all 0.3s ease;
    }

    .leaderboard-table tbody tr:hover {
        background: rgba(255, 255, 255, 0.05);
        transform: scale(1.01);
    }

    .rank-header {
        width: 80px;
        text-align: center;
    }

    .profile-header {
        width: 100px;
        text-align: center;
    }

    .name-header {
        min-width: 200px;
    }

    .score-header {
        width: 120px;
        text-align: center;
    }

    .category-header {
        width: 100px;
        text-align: center;
    }

    .rank-cell {
        text-align: center;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .rank-1 {
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .rank-2 {
        background: linear-gradient(135deg, #c0c0c0, #e5e5e5);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .rank-3 {
        background: linear-gradient(135deg, #cd7f32, #daa520);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .profile-pic {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .profile-pic:hover {
        transform: scale(1.1);
        border-color: var(--primary-blue);
    }

    .score-cell {
        text-align: center;
        font-weight: bold;
        font-size: 1.1rem;
    }

    .category-score {
        text-align: center;
        font-weight: 500;
        padding: 0.25rem 0.5rem;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        display: inline-block;
        min-width: 30px;
    }

    .current-user {
        background: rgba(14, 165, 233, 0.2) !important;
        border-left: 4px solid var(--primary-blue);
    }

    .current-user:hover {
        background: rgba(14, 165, 233, 0.3) !important;
    }

    .score-update {
        animation: scoreUpdate 1.5s ease;
    }

    @keyframes scoreUpdate {
        0% {
            background: rgba(255, 193, 7, 0.3);
        }

        50% {
            background: rgba(255, 193, 7, 0.5);
        }

        100% {
            background: transparent;
        }
    }

    .score-up {
        color: #28a745;
        font-weight: bold;
        margin-left: 0.5rem;
    }

    .loading-spinner {
        color: var(--primary-blue);
    }

    .no-data-icon {
        color: rgba(255, 255, 255, 0.5);
    }

    @media (max-width: 768px) {
        .leaderboard-table {
            font-size: 0.9rem;
        }

        .leaderboard-table th,
        .leaderboard-table td {
            padding: 0.5rem;
        }

        .profile-pic {
            width: 40px;
            height: 40px;
        }

        .stat-card {
            padding: 1rem;
        }

        .stat-card h4 {
            font-size: 1.5rem;
        }
    }
</style>

<script>
    const userEmail = "{{ session['user_email'] }}";
    let previousScores = {};
    let previousCategoryScores = {};
    let updateCount = 0;

    async function fetchLeaderboard() {
        try {
            showLoading();
            console.log("Fetching leaderboard data...");

            const response = await fetch("/leaderboard_data");
            console.log("Response status:", response.status);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log("Leaderboard data:", result);

            if (!result.success) {
                console.error("Leaderboard fetch failed:", result.error);
                showError();
                return;
            }

            const data = result.data || [];
            console.log("Participants data:", data);

            if (data.length === 0) {
                showNoData();
                return;
            }

            updateStats(data);
            updateTable(data);
            updateCount++;

        } catch (err) {
            console.error("Failed to fetch leaderboard:", err);
            showError();
        }
    }

    function updateStats(data) {
        const totalParticipants = data.length;
        const avgScore = data.reduce((sum, p) => sum + (p.score || 0), 0) / totalParticipants;
        const topScore = Math.max(...data.map(p => p.score || 0));

        document.getElementById('totalParticipants').textContent = totalParticipants;
        document.getElementById('avgScore').textContent = avgScore.toFixed(1);
        document.getElementById('topScore').textContent = topScore;
        document.getElementById('lastUpdated').textContent = updateCount === 0 ? 'Live' : 'Just now';
    }

    function updateTable(data) {
        const tbody = document.getElementById("leaderboard-body");
        tbody.innerHTML = "";

        data.forEach((p, index) => {
            const row = document.createElement("tr");
            const isCurrentUser = (p.email === userEmail);

            if (isCurrentUser) {
                row.classList.add('current-user');
            }

            const mathScore = (p.category_scores && p.category_scores.Math) || 0;
            const reasoningScore = (p.category_scores && p.category_scores.Reasoning) || 0;
            const verbalScore = (p.category_scores && p.category_scores.Verbal) || 0;
            const totalScore = p.score || 0;

            row.innerHTML = `
                <td class="rank-cell ${index < 3 ? `rank-${index + 1}` : ''}">
                    ${index + 1}
                </td>
                <td class="text-center">
                    ${p.profile_pic ?
                    `<img src="${p.profile_pic}" alt="Profile" class="profile-pic">` :
                    '<div class="profile-pic bg-secondary d-flex align-items-center justify-content-center"><i class="fas fa-user"></i></div>'
                }
                </td>
                <td>
                    <strong>${p.name || "Unknown"}</strong>
                    ${isCurrentUser ? '<span class="badge bg-primary ms-2">You</span>' : ''}
                </td>
                <td class="score-cell total-score">${totalScore}</td>
                <td class="text-center">
                    <span class="category-score math-score">${mathScore}</span>
                </td>
                <td class="text-center">
                    <span class="category-score reasoning-score">${reasoningScore}</span>
                </td>
                <td class="text-center">
                    <span class="category-score verbal-score">${verbalScore}</span>
                </td>
            `;

            if (isCurrentUser) {
                if (!previousCategoryScores[p.email]) previousCategoryScores[p.email] = {};

                // Animate total score
                if (previousScores[p.email] !== undefined && previousScores[p.email] !== totalScore) {
                    animateRowCell(row.querySelector(".total-score"));
                }

                // Animate categories if improved
                checkCategory(row.querySelector(".math-score"), mathScore, "Math", p.email);
                checkCategory(row.querySelector(".reasoning-score"), reasoningScore, "Reasoning", p.email);
                checkCategory(row.querySelector(".verbal-score"), verbalScore, "Verbal", p.email);

                previousCategoryScores[p.email] = { Math: mathScore, Reasoning: reasoningScore, Verbal: verbalScore };
                previousScores[p.email] = totalScore;
            }

            tbody.appendChild(row);
        });

        hideLoading();
    }

    function checkCategory(cell, currentScore, category, email) {
        const prev = (previousCategoryScores[email] && previousCategoryScores[email][category]) || 0;
        if (currentScore > prev) {
            cell.innerHTML += ' <span class="score-up">↑</span>';
            animateRowCell(cell);
        }
    }

    function animateRowCell(cell) {
        cell.classList.add("score-update");
        setTimeout(() => {
            cell.classList.remove("score-update");
        }, 1500);
    }

    function showLoading() {
        document.getElementById('loadingState').style.display = 'block';
        document.getElementById('noDataState').style.display = 'none';
    }

    function hideLoading() {
        document.getElementById('loadingState').style.display = 'none';
    }

    function showNoData() {
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('noDataState').style.display = 'block';
    }

    function showError() {
        hideLoading();
        const tbody = document.getElementById("leaderboard-body");
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Failed to load leaderboard data. Please refresh the page.
                </td>
            </tr>
        `;
    }

    // Initial load
    fetchLeaderboard();

    // Refresh every 10 seconds
    setInterval(fetchLeaderboard, 10000);
</script>
{% endblock %}
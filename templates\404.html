{% extends "base.html" %}
{% block title %}Page Not Found - ITian Club{% endblock %}

{% block content %}
<div class="error-container">
    <div class="row justify-content-center">
        <div class="col-lg-8 text-center">
            <div class="enhanced-error-card glass-card fade-in-up">
                <!-- Animated Error Icon -->
                <div class="error-visual mb-4">
                    <div class="error-number">404</div>
                    <div class="error-icon-container">
                        <div class="error-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="floating-elements">
                            <div class="floating-dot dot-1"></div>
                            <div class="floating-dot dot-2"></div>
                            <div class="floating-dot dot-3"></div>
                        </div>
                    </div>
                </div>

                <!-- Error Content -->
                <div class="error-content mb-4">
                    <h1 class="error-title mb-3">Page Not Found</h1>
                    <p class="error-description mb-4">
                        Oops! The page you're looking for seems to have wandered off into the digital void.
                    </p>
                    <div class="error-suggestions">
                        <h6 class="suggestions-title mb-3">Here's what you can do:</h6>
                        <div class="suggestions-list">
                            <div class="suggestion-item">
                                <i class="fas fa-check-circle me-2"></i>
                                <span>Check the URL for typos</span>
                            </div>
                            <div class="suggestion-item">
                                <i class="fas fa-check-circle me-2"></i>
                                <span>Go back to the previous page</span>
                            </div>
                            <div class="suggestion-item">
                                <i class="fas fa-check-circle me-2"></i>
                                <span>Visit our homepage</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Action Buttons -->
                <div class="error-actions">
                    <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                        <a href="{{ url_for('index') }}" class="btn btn-premium btn-lg">
                            <i class="fas fa-home me-2"></i>
                            <span>Go to Home</span>
                        </a>
                        <button onclick="history.back()" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>
                            <span>Go Back</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Enhanced Error Page Styles */
    .error-container {
        min-height: 80vh;
        display: flex;
        align-items: center;
        padding: var(--space-2xl) 0;
    }

    .enhanced-error-card {
        background: var(--bg-glass);
        backdrop-filter: blur(30px);
        -webkit-backdrop-filter: blur(30px);
        border: 1px solid var(--border-secondary);
        box-shadow: var(--shadow-2xl);
        position: relative;
        overflow: hidden;
    }

    .enhanced-error-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--accent-red), var(--accent-yellow), var(--primary-blue));
    }

    .error-visual {
        position: relative;
        margin-bottom: var(--space-2xl);
    }

    .error-number {
        font-size: 8rem;
        font-weight: 900;
        color: var(--text-primary);
        opacity: 0.1;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
        animation: pulse 3s infinite;
    }

    .error-icon-container {
        position: relative;
        z-index: 2;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 150px;
    }

    .error-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--accent-red), var(--accent-yellow));
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-primary);
        font-size: var(--font-size-3xl);
        box-shadow: var(--shadow-xl);
        animation: float 3s ease-in-out infinite;
    }

    .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
    }

    .floating-dot {
        position: absolute;
        width: 8px;
        height: 8px;
        background: var(--primary-blue);
        border-radius: var(--radius-full);
        opacity: 0.6;
    }

    .dot-1 {
        top: 20%;
        left: 20%;
        animation: float 2s ease-in-out infinite;
    }

    .dot-2 {
        top: 30%;
        right: 25%;
        animation: float 2s ease-in-out infinite 0.5s;
    }

    .dot-3 {
        bottom: 25%;
        left: 30%;
        animation: float 2s ease-in-out infinite 1s;
    }

    .error-title {
        font-size: var(--font-size-4xl);
        font-weight: 800;
        color: var(--text-primary);
    }

    .error-description {
        font-size: var(--font-size-lg);
        color: var(--text-secondary);
        line-height: 1.6;
    }

    .error-suggestions {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-xl);
        padding: var(--space-xl);
        margin: var(--space-xl) 0;
    }

    .suggestions-title {
        color: var(--text-primary);
        font-weight: 600;
    }

    .suggestions-list {
        display: flex;
        flex-direction: column;
        gap: var(--space-sm);
    }

    .suggestion-item {
        display: flex;
        align-items: center;
        color: var(--text-secondary);
        font-size: var(--font-size-base);
    }

    .suggestion-item i {
        color: var(--accent-green);
    }

    @keyframes float {

        0%,
        100% {
            transform: translateY(0px);
        }

        50% {
            transform: translateY(-10px);
        }
    }
</style>
{% endblock %}
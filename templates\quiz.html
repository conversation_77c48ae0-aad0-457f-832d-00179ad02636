{% extends "base.html" %}
{% block title %}Aptitude Quiz - ITian Club{% endblock %}

{% block content %}
<div class="quiz-container">
    <!-- Enhanced Quiz Header -->
    <div class="quiz-header glass-card mb-4 fade-in-up">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="user-welcome">
                    <div class="user-avatar">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="welcome-content">
                        <h2 class="quiz-heading mb-1">
                            Welcome, {{ session.user_name }}!
                        </h2>
                        <p class="quiz-subtitle mb-0">Ready to test your aptitude skills?</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="timer-container">
                    <div class="timer-display">
                        <div class="timer-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="timer-content">
                            <span class="timer-label">Time Remaining</span>
                            <span id="timer" class="timer-text">{{ "%02d" % (timer // 60) }}:{{ "%02d" % (timer % 60)
                                }}</span>
                        </div>
                    </div>
                    <div class="timer-progress">
                        <div id="timerProgress" class="timer-bar"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Progress Bar -->
    <div class="quiz-progress glass-card mb-4 reveal">
        <div class="progress-info">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="progress-stats">
                    <span class="progress-text">Question <span id="currentQuestion">1</span> of {{ questions|length
                        }}</span>
                    <div class="progress-dots mt-2">
                        {% for i in range(questions|length) %}
                        <span class="progress-dot {{ 'active' if loop.index == 1 else '' }}"
                            data-question="{{ loop.index }}"></span>
                        {% endfor %}
                    </div>
                </div>
                <div class="progress-percentage-container">
                    <span class="progress-percentage"><span id="progressPercentage">0</span>%</span>
                    <span class="progress-label">Complete</span>
                </div>
            </div>
            <div class="progress-container">
                <div id="progress" class="progress-bar"></div>
            </div>
        </div>
    </div>

    <!-- Quiz Form -->
    <form method="POST" id="quizForm" data-timer="{{ timer }}">
        {% for q in questions %}
        <div class="question-card enhanced-card glass-card reveal" data-index="{{ loop.index0 }}"
            style="display: {{ 'block' if loop.index == 1 else 'none' }};">
            <!-- Enhanced Question Header -->
            <div class="question-header mb-4">
                <div class="header-content">
                    <div class="category-badge">
                        <div class="category-icon">
                            <i
                                class="fas fa-{{ 'calculator' if q.category == 'Math' else 'brain' if q.category == 'Reasoning' else 'language' }}"></i>
                        </div>
                        <span class="category-text">{{ q.category }}</span>
                    </div>
                    <div class="question-meta">
                        <span class="question-number">{{ loop.index }}/{{ questions|length }}</span>
                        <div class="difficulty-indicator">
                            {% for i in range(3) %}
                            <span class="difficulty-dot {{ 'active' if i < 2 else '' }}"></span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Question Content -->
            <div class="question-content mb-4">
                <h4 class="question-text">{{ q.question }}</h4>
                {% if q.category == 'Math' %}
                <div class="question-hint">
                    <i class="fas fa-lightbulb me-2"></i>
                    <span>Take your time to calculate accurately</span>
                </div>
                {% endif %}
            </div>

            <!-- Enhanced Options -->
            <div class="options-container">
                <div class="options-header mb-3">
                    <div class="instruction-badge">
                        <i class="fas fa-{{ 'check-square' if q.multiple else 'dot-circle' }} me-2"></i>
                        <span>{{ 'Select all that apply' if q.multiple else 'Choose the best answer' }}</span>
                    </div>
                </div>

                <div class="options-grid">
                    {% if q.multiple %}
                    {% for option in q.options %}
                    <label class="option-item enhanced-option">
                        <input type="checkbox" name="q{{ q.id }}" value="{{ option }}" class="option-input">
                        <div class="option-content">
                            <div class="option-indicator">
                                <div class="checkbox-custom">
                                    <i class="fas fa-check"></i>
                                </div>
                            </div>
                            <span class="option-text">{{ option }}</span>
                        </div>
                    </label>
                    {% endfor %}
                    {% else %}
                    {% for option in q.options %}
                    <label class="option-item enhanced-option">
                        <input type="radio" name="q{{ q.id }}" value="{{ option }}" required class="option-input">
                        <div class="option-content">
                            <div class="option-indicator">
                                <div class="radio-custom">
                                    <div class="radio-dot"></div>
                                </div>
                            </div>
                            <span class="option-text">{{ option }}</span>
                        </div>
                    </label>
                    {% endfor %}
                    {% endif %}
                </div>
            </div>

        </div>
        {% endfor %}

        <!-- Enhanced Navigation -->
        <div class="question-navigation enhanced-nav glass-card mt-4">
            <div class="nav-content">
                <div class="nav-section nav-left">
                    <button type="button" class="btn btn-nav btn-outline-light" id="prevBtn" style="display: none;">
                        <i class="fas fa-chevron-left me-2"></i>
                        <span>Previous</span>
                    </button>
                </div>

                <div class="nav-section nav-center">
                    <div class="question-indicators">
                        {% for i in range(questions|length) %}
                        <button type="button" class="question-indicator {{ 'active' if loop.index == 1 else '' }}"
                            data-question="{{ i + 1 }}">
                            <span class="indicator-number">{{ i + 1 }}</span>
                            <div class="indicator-status">
                                <i class="fas fa-check"></i>
                            </div>
                        </button>
                        {% endfor %}
                    </div>
                </div>

                <div class="nav-section nav-right">
                    <button type="button" class="btn btn-nav btn-premium" id="nextBtn">
                        <span>Next</span>
                        <i class="fas fa-chevron-right ms-2"></i>
                    </button>

                    <button type="submit" class="btn btn-nav btn-success" id="submitBtn" style="display: none;">
                        <i class="fas fa-paper-plane me-2"></i>
                        <span>Submit Quiz</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Submit Section -->
        <div class="submit-section glass-card reveal" style="display: none;">
            <div class="text-center">
                <h3 class="mb-4">
                    <i class="fas fa-check-circle text-success me-2"></i>
                    Quiz Complete!
                </h3>
                <p class="lead mb-4">You've answered all questions. Ready to submit your answers?</p>

                <div class="summary-stats mb-4">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="stat-item">
                                <h4 class="text-primary">{{ questions|length }}</h4>
                                <p>Total Questions</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-item">
                                <h4 class="text-primary" id="answeredCount">0</h4>
                                <p>Answered</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stat-item">
                                <h4 class="text-primary" id="remainingCount">{{ questions|length }}</h4>
                                <p>Remaining</p>
                            </div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-premium btn-lg px-5">
                    <i class="fas fa-paper-plane me-2"></i>
                    Submit Quiz
                </button>
            </div>
        </div>
    </form>
</div>

<style>
    /* Enhanced Quiz Container */
    .quiz-container {
        max-width: 900px;
        margin: 0 auto;
        padding: var(--space-lg);
    }

    /* Enhanced Quiz Header */
    .quiz-header {
        background: var(--bg-glass);
        backdrop-filter: blur(30px);
        -webkit-backdrop-filter: blur(30px);
        border: 1px solid var(--border-secondary);
        box-shadow: var(--shadow-xl);
        position: relative;
        overflow: hidden;
    }

    .quiz-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--primary-blue), var(--primary-purple), var(--accent-cyan));
    }

    .user-welcome {
        display: flex;
        align-items: center;
        gap: var(--space-lg);
    }

    .user-avatar {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-primary);
        font-size: var(--font-size-xl);
        box-shadow: var(--shadow-lg);
        animation: pulse 2s infinite;
    }

    .quiz-heading {
        font-size: var(--font-size-2xl);
        font-weight: 700;
        color: var(--text-primary);
        margin: 0;
    }

    .quiz-subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-base);
        margin: 0;
    }

    /* Enhanced Timer */
    .timer-container {
        text-align: center;
    }

    .timer-display {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-md);
        background: var(--bg-glass);
        border-radius: var(--radius-xl);
        padding: var(--space-lg);
        margin-bottom: var(--space-md);
        backdrop-filter: blur(20px);
        border: 1px solid var(--border-secondary);
        box-shadow: var(--shadow-lg);
    }

    .timer-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, var(--accent-yellow), #fbbf24);
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-dark);
        font-size: var(--font-size-lg);
        animation: tick 1s infinite;
    }

    @keyframes tick {

        0%,
        50% {
            transform: scale(1);
        }

        25% {
            transform: scale(1.1);
        }
    }

    .timer-content {
        text-align: left;
    }

    .timer-label {
        display: block;
        font-size: var(--font-size-sm);
        color: var(--text-muted);
        margin-bottom: var(--space-xs);
    }

    .timer-text {
        font-size: var(--font-size-2xl);
        font-weight: 700;
        color: var(--text-primary);
        font-family: 'Courier New', monospace;
    }

    .timer-progress {
        width: 100%;
        height: 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-full);
        overflow: hidden;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .timer-bar {
        height: 100%;
        background: linear-gradient(90deg, var(--accent-green), var(--accent-yellow), var(--accent-red));
        border-radius: var(--radius-full);
        transition: width 1s linear;
        box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
    }

    /* Enhanced Progress Section */
    .quiz-progress {
        background: var(--bg-glass);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 1px solid var(--border-secondary);
        box-shadow: var(--shadow-lg);
    }

    .progress-dots {
        display: flex;
        gap: var(--space-xs);
        flex-wrap: wrap;
    }

    .progress-dot {
        width: 8px;
        height: 8px;
        border-radius: var(--radius-full);
        background: rgba(255, 255, 255, 0.2);
        transition: all var(--transition-base);
        cursor: pointer;
    }

    .progress-dot.active {
        background: var(--primary-blue);
        box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
    }

    .progress-dot.completed {
        background: var(--accent-green);
        box-shadow: 0 0 8px rgba(16, 185, 129, 0.5);
    }

    .progress-percentage-container {
        text-align: right;
    }

    .progress-percentage {
        font-size: var(--font-size-2xl);
        font-weight: 700;
        color: var(--primary-blue);
    }

    .progress-label {
        display: block;
        font-size: var(--font-size-sm);
        color: var(--text-muted);
        margin-top: var(--space-xs);
    }

    .progress-container {
        width: 100%;
        height: 10px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--radius-full);
        overflow: hidden;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, var(--primary-blue), var(--primary-purple));
        border-radius: var(--radius-full);
        transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
        width: 0%;
    }

    /* Enhanced Question Cards */
    .enhanced-card {
        background: var(--bg-glass);
        backdrop-filter: blur(30px);
        -webkit-backdrop-filter: blur(30px);
        border: 1px solid var(--border-secondary);
        box-shadow: var(--shadow-xl);
        position: relative;
        overflow: hidden;
        transition: all var(--transition-base);
    }

    .enhanced-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-blue), var(--primary-purple));
    }

    .enhanced-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-2xl);
        border-color: var(--border-accent);
    }

    .question-header {
        border-bottom: 1px solid var(--border-primary);
        padding-bottom: var(--space-lg);
    }

    .header-content {
        display: flex;
        justify-content: between;
        align-items: center;
        gap: var(--space-lg);
    }

    .category-badge {
        display: flex;
        align-items: center;
        gap: var(--space-sm);
        padding: var(--space-sm) var(--space-lg);
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
        border-radius: var(--radius-full);
        color: var(--text-primary);
        font-size: var(--font-size-sm);
        font-weight: 600;
        box-shadow: var(--shadow-md);
    }

    .category-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: var(--radius-full);
        font-size: var(--font-size-sm);
    }

    .question-meta {
        display: flex;
        align-items: center;
        gap: var(--space-md);
        margin-left: auto;
    }

    .question-number {
        font-size: var(--font-size-sm);
        color: var(--text-muted);
        font-weight: 500;
    }

    .difficulty-indicator {
        display: flex;
        gap: var(--space-xs);
    }

    .difficulty-dot {
        width: 6px;
        height: 6px;
        border-radius: var(--radius-full);
        background: rgba(255, 255, 255, 0.2);
        transition: all var(--transition-base);
    }

    .difficulty-dot.active {
        background: var(--accent-yellow);
        box-shadow: 0 0 6px rgba(251, 191, 36, 0.5);
    }

    .progress-text,
    .progress-percentage {
        color: #ffffff;
        font-weight: 600;
    }

    .question-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .category-badge {
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .question-number {
        color: #d1d5db;
        font-weight: 600;
    }

    .question-text {
        color: #ffffff;
        font-size: 1.2rem;
        line-height: 1.6;
        margin-bottom: 0;
    }

    .options-container {
        margin-top: 2rem;
    }

    .option-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        margin-bottom: 0.5rem;
        background: rgba(17, 24, 39, 0.6);
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
    }

    .option-item:hover {
        background: rgba(37, 99, 235, 0.2);
        border-color: var(--primary-blue);
        transform: translateX(5px);
    }

    .option-input {
        display: none;
    }

    .option-text {
        color: #ffffff;
        font-weight: 500;
        margin-left: 1rem;
        flex: 1;
    }

    .option-checkmark {
        width: 24px;
        height: 24px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: transparent;
        transition: all 0.3s ease;
    }

    .option-item.selected .option-checkmark {
        background: var(--primary-blue);
        border-color: var(--primary-blue);
        color: white;
    }

    .question-navigation {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 1.5rem;
    }

    .question-indicators {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .question-indicator {
        width: 40px;
        height: 40px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        background: rgba(17, 24, 39, 0.6);
        border-radius: 50%;
        color: #ffffff;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .question-indicator:hover {
        background: rgba(37, 99, 235, 0.2);
        border-color: var(--primary-blue);
    }

    .question-indicator.active {
        background: var(--primary-blue);
        border-color: var(--primary-blue);
    }

    .question-indicator.answered {
        background: var(--primary-purple);
        border-color: var(--primary-purple);
    }

    .summary-stats {
        background: rgba(17, 24, 39, 0.6);
        border-radius: 15px;
        padding: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .stat-item {
        text-align: center;
    }

    .stat-item h4 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .stat-item p {
        color: #d1d5db;
        margin-bottom: 0;
    }

    @media (max-width: 768px) {
        .question-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .question-navigation {
            flex-direction: column;
            gap: 1rem;
        }

        .question-indicators {
            order: -1;
        }

        .timer-text {
            font-size: 1.2rem;
        }
    }
</style>

<!-- Quiz functionality is handled by script.js -->
{% endblock %}
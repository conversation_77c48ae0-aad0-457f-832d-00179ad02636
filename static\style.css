/* =================================
   ENHANCED MODERN CSS UTILITIES
   ================================= */

/* Modern CSS Reset and Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
}

body {
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Enhanced Utility Classes */
.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.sr-only {
    @extend .visually-hidden;
}

/* Focus Management */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-blue);
    color: var(--text-primary);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
}

.skip-link:focus {
    top: 6px;
}

/* Enhanced Quiz Container */
.quiz-container {
    max-width: 900px;
    margin: 0 auto;
    padding: var(--space-2xl);
    background: var(--bg-glass);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-radius: var(--radius-2xl);
    border: 1px solid var(--border-secondary);
    box-shadow: var(--shadow-2xl);
    font-family: var(--font-family-base);
    position: relative;
    overflow: hidden;
}

.quiz-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-purple), var(--accent-cyan));
}

.quiz-heading {
    text-align: center;
    color: var(--text-primary);
    margin-bottom: var(--space-xl);
    font-size: var(--font-size-3xl);
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.timer {
    text-align: center;
    font-weight: 600;
    color: var(--accent-red);
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-xl);
    padding: var(--space-md);
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

/* =================================
   ENHANCED QUESTION CARD
   ================================= */
.question-card {
    background: var(--bg-glass);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border-radius: var(--radius-2xl);
    padding: var(--space-2xl);
    margin-bottom: var(--space-xl);
    border: 1px solid var(--border-secondary);
    border-left: 4px solid var(--accent-green);
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.question-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05));
    opacity: 0;
    transition: opacity var(--transition-base);
}

.question-card:hover {
    transform: translateY(-3px) scale(1.01);
    box-shadow: var(--shadow-2xl);
    border-color: var(--border-accent);
}

.question-card:hover::before {
    opacity: 1;
}

.question-card.active {
    border-left-color: var(--primary-blue);
    box-shadow: var(--shadow-glow);
}

.category {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-bottom: var(--space-sm);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.05em;
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    border: 1px solid var(--border-primary);
}

.question-text {
    font-size: 1.1em;
    margin-bottom: 10px;
}

.options {
    display: flex;
    flex-direction: column;
}

.option-label {
    padding: 8px 10px;
    margin-bottom: 6px;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.option-label:hover {
    background: #f0f0f0;
}

/* =================================
   SUBMIT BUTTON
   ================================= */
.submit-btn {
    display: block;
    width: 100%;
    padding: 12px 0;
    font-size: 1.1em;
    border: none;
    border-radius: 8px;
    background: #4CAF50;
    color: white;
    cursor: pointer;
    transition: background 0.3s ease;
}

.submit-btn:hover {
    background: #45a049;
}

/* =================================
   PROGRESS BAR
   ================================= */
.progress-bar {
    width: 100%;
    background-color: #eee;
    border-radius: 10px;
    margin: 15px 0;
    height: 15px;
    overflow: hidden;
}

#progress {
    height: 100%;
    background-color: #4CAF50;
    width: 0%;
    transition: width 0.5s ease;
}

/* =================================
   LEADERBOARD
   ================================= */
.leaderboard-container {
    max-width: 900px;
    margin: 20px auto;
    padding: 20px;
    background: #fdfdfd;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.leaderboard-container table {
    width: 100%;
    border-collapse: collapse;
}

.leaderboard-container th,
.leaderboard-container td {
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid #ddd;
}

.leaderboard-container th {
    background-color: #4CAF50;
    color: white;
}

.leaderboard-container tr:hover {
    background-color: #f1f1f1;
}

/* =================================
   PARTICIPANT HIGHLIGHT & SCORE ANIMATIONS
   ================================= */
.highlight-participant {
    transition: background 0.5s ease;
}

.score-update {
    background-color: #d4edda;
    /* light green */
    transition: background 1.5s ease;
}

.score-up {
    color: #28a745;
    font-weight: bold;
    margin-left: 4px;
}
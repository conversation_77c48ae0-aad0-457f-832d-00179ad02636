{% extends "base.html" %}
{% block title %}Home - ITian Club Aptitude Quiz{% endblock %}

{% block content %}
<!-- Enhanced Hero Section -->
<div class="hero-section">
    <div class="row align-items-center min-vh-100">
        <div class="col-lg-6 fade-in-up">
            <div class="glass-card hero-card">
                <div class="hero-badge mb-4">
                    <i class="fas fa-star me-2"></i>
                    <span>Premium Aptitude Assessment</span>
                </div>
                <h1 class="hero-title mb-4">
                    Welcome to the
                    <span class="text-gradient">ITian Club</span>
                    <br>Aptitude Quiz Event!
                </h1>
                <p class="hero-description mb-4">
                    Test your skills, challenge your mind, and discover your potential.
                    Join hundreds of students competing in our comprehensive aptitude assessment
                    designed to evaluate your analytical thinking and problem-solving abilities.
                </p>

                <!-- Enhanced Features Grid -->
                <div class="hero-features mb-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-brain"></i>
                                </div>
                                <div class="feature-content">
                                    <h6>Mental Agility</h6>
                                    <small>Test your reasoning skills</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-calculator"></i>
                                </div>
                                <div class="feature-content">
                                    <h6>Mathematical Prowess</h6>
                                    <small>Solve complex problems</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-language"></i>
                                </div>
                                <div class="feature-content">
                                    <h6>Verbal Excellence</h6>
                                    <small>Master language skills</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-trophy"></i>
                                </div>
                                <div class="feature-content">
                                    <h6>Win Prizes</h6>
                                    <small>Compete for rewards</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced CTA Buttons -->
                <div class="hero-cta">
                    <div class="d-flex flex-column flex-sm-row gap-3">
                        <a href="{{ url_for('google.login') }}" class="btn btn-premium btn-lg">
                            <i class="fas fa-rocket me-2"></i>
                            Start Quiz Now
                        </a>
                        <a href="#about" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-info-circle me-2"></i>
                            Learn More
                        </a>
                    </div>
                    <div class="hero-stats mt-4">
                        <div class="d-flex justify-content-center gap-4">
                            <div class="stat-item">
                                <span class="stat-number">500+</span>
                                <span class="stat-label">Students</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">95%</span>
                                <span class="stat-label">Success Rate</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">24/7</span>
                                <span class="stat-label">Available</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 fade-in">
            <div class="hero-visual">
                <div class="floating-elements">
                    <div class="floating-card main-card">
                        <div class="card-icon">
                            <i class="fas fa-brain fa-3x"></i>
                        </div>
                        <h5>Aptitude Assessment</h5>
                        <p>Challenge your analytical thinking</p>
                        <div class="card-progress">
                            <div class="progress-bar"></div>
                        </div>
                    </div>
                    <div class="floating-card secondary-card">
                        <i class="fas fa-trophy fa-2x text-warning"></i>
                        <span>Top Performer</span>
                    </div>
                    <div class="floating-card tertiary-card">
                        <i class="fas fa-clock fa-lg"></i>
                        <span>5 Min</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- About Section -->
    <div id="about" class="row mt-5 pt-5 reveal">
        <div class="col-12 text-center mb-5">
            <h2 class="display-5 fw-bold mb-3">Why Take Our Aptitude Test?</h2>
            <p class="lead">Discover your strengths and areas for improvement</p>
        </div>

        <div class="col-md-4 mb-4">
            <div class="glass-card text-center h-100">
                <div class="feature-icon-large mb-3">
                    <i class="fas fa-chart-line fa-2x text-primary"></i>
                </div>
                <h4>Performance Analytics</h4>
                <p>Get detailed insights into your performance across different categories with comprehensive analytics.
                </p>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="glass-card text-center h-100">
                <div class="feature-icon-large mb-3">
                    <i class="fas fa-users fa-2x text-primary"></i>
                </div>
                <h4>Peer Comparison</h4>
                <p>See how you rank among your peers and identify areas where you excel or need improvement.</p>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="glass-card text-center h-100">
                <div class="feature-icon-large mb-3">
                    <i class="fas fa-certificate fa-2x text-primary"></i>
                </div>
                <h4>Certification</h4>
                <p>Earn a certificate of completion and recognition for your participation and performance.</p>
            </div>
        </div>
    </div>

    <!-- Stats Section -->
    <div class="row mt-5 reveal">
        <div class="col-12">
            <div class="glass-card">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="stat-item">
                            <h3 class="display-6 fw-bold text-primary">500+</h3>
                            <p class="text-muted">Students Participated</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-item">
                            <h3 class="display-6 fw-bold text-primary">95%</h3>
                            <p class="text-muted">Success Rate</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-item">
                            <h3 class="display-6 fw-bold text-primary">3</h3>
                            <p class="text-muted">Test Categories</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="stat-item">
                            <h3 class="display-6 fw-bold text-primary">24/7</h3>
                            <p class="text-muted">Available</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- How It Works -->
    <div class="row mt-5 reveal">
        <div class="col-12 text-center mb-5">
            <h2 class="display-5 fw-bold mb-3">How It Works</h2>
            <p class="lead">Simple steps to get started</p>
        </div>

        <div class="col-md-3 mb-4">
            <div class="glass-card text-center h-100">
                <div class="step-number mb-3">1</div>
                <h5>Sign In</h5>
                <p>Use your Google account to quickly sign in and get started.</p>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="glass-card text-center h-100">
                <div class="step-number mb-3">2</div>
                <h5>Complete Profile</h5>
                <p>Fill in your details including URN/CRN and branch information.</p>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="glass-card text-center h-100">
                <div class="step-number mb-3">3</div>
                <h5>Take Quiz</h5>
                <p>Answer questions across Math, Reasoning, and Verbal categories.</p>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="glass-card text-center h-100">
                <div class="step-number mb-3">4</div>
                <h5>Get Results</h5>
                <p>Receive your score and detailed performance analysis.</p>
            </div>
        </div>
    </div>

    <style>
        /* Enhanced Hero Section Styles */
        .hero-section {
            position: relative;
            overflow: hidden;
        }

        .hero-card {
            position: relative;
            background: var(--bg-glass);
            backdrop-filter: blur(30px);
            -webkit-backdrop-filter: blur(30px);
            border: 1px solid var(--border-secondary);
            box-shadow: var(--shadow-2xl);
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            padding: var(--space-sm) var(--space-lg);
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
            border-radius: var(--radius-full);
            color: var(--text-primary);
            font-size: var(--font-size-sm);
            font-weight: 600;
            box-shadow: var(--shadow-lg);
            animation: pulse 2s infinite;
        }

        .hero-title {
            font-size: var(--font-size-5xl);
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: var(--space-lg);
        }

        .text-gradient {
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple), var(--accent-cyan));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradient-shift 3s ease-in-out infinite;
        }

        @keyframes gradient-shift {

            0%,
            100% {
                filter: hue-rotate(0deg);
            }

            50% {
                filter: hue-rotate(30deg);
            }
        }

        .hero-description {
            font-size: var(--font-size-xl);
            line-height: 1.7;
            color: var(--text-secondary);
            margin-bottom: var(--space-2xl);
        }

        /* Enhanced Feature Items */
        .hero-features {
            margin-bottom: var(--space-2xl);
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            padding: var(--space-lg);
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            transition: all var(--transition-base);
            position: relative;
            overflow: hidden;
        }

        .feature-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            transition: left var(--transition-slow);
        }

        .feature-item:hover::before {
            left: 100%;
        }

        .feature-item:hover {
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--border-accent);
            box-shadow: var(--shadow-lg);
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-primary);
            font-size: var(--font-size-xl);
            box-shadow: var(--shadow-md);
            transition: all var(--transition-base);
        }

        .feature-item:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: var(--shadow-glow);
        }

        .feature-content h6 {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: var(--space-xs);
            font-size: var(--font-size-base);
        }

        .feature-content small {
            color: var(--text-muted);
            font-size: var(--font-size-sm);
        }

        /* Hero CTA and Stats */
        .hero-cta {
            margin-top: var(--space-2xl);
        }

        .hero-stats {
            padding: var(--space-lg);
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-xl);
            backdrop-filter: blur(10px);
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: var(--font-size-2xl);
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: var(--space-xs);
        }

        .stat-label {
            font-size: var(--font-size-sm);
            color: var(--text-muted);
            font-weight: 500;
        }

        /* Enhanced Hero Visual */
        .hero-visual {
            position: relative;
            height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-elements {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .floating-card {
            position: absolute;
            background: var(--bg-glass);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-2xl);
            padding: var(--space-xl);
            box-shadow: var(--shadow-xl);
            transition: all var(--transition-base);
        }

        .main-card {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 280px;
            text-align: center;
            animation: float 6s ease-in-out infinite;
        }

        .secondary-card {
            top: 20%;
            right: 10%;
            width: 120px;
            text-align: center;
            animation: float 6s ease-in-out infinite 2s;
        }

        .tertiary-card {
            bottom: 20%;
            left: 10%;
            width: 100px;
            text-align: center;
            animation: float 6s ease-in-out infinite 4s;
        }

        .card-icon {
            margin-bottom: var(--space-lg);
            color: var(--primary-blue);
        }

        .card-progress {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-full);
            overflow: hidden;
            margin-top: var(--space-lg);
        }

        .card-progress .progress-bar {
            width: 75%;
            height: 100%;
            background: linear-gradient(90deg, var(--primary-blue), var(--primary-purple));
            border-radius: var(--radius-full);
            animation: progress-fill 3s ease-in-out infinite;
        }

        @keyframes progress-fill {

            0%,
            100% {
                width: 75%;
            }

            50% {
                width: 85%;
            }
        }

        .floating-card:hover {
            transform: translate(-50%, -50%) scale(1.05);
            box-shadow: var(--shadow-glow);
        }

        .secondary-card:hover,
        .tertiary-card:hover {
            transform: scale(1.1);
        }

        /* Feature Icon Large */
        .feature-icon-large {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
            border-radius: var(--radius-2xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-primary);
            margin: 0 auto;
            box-shadow: var(--shadow-lg);
            transition: all var(--transition-base);
        }

        .feature-icon-large:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: var(--shadow-glow);
        }

        .step-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0 auto;
        }

        .stat-item {
            padding: 1rem;
        }

        .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            background: transparent;
            transition: all 0.3s ease;
        }

        .btn-outline-light:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .hero-image {
                height: 300px;
                margin-top: 2rem;
            }

            .floating-card {
                padding: 1.5rem;
            }
        }
    </style>
    {% endblock %}
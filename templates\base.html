<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Aptitude Quiz{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
        rel="stylesheet">

    <style>
        :root {
            /* Modern Color Palette */
            --primary-blue: #3b82f6;
            --primary-purple: #8b5cf6;
            --secondary-blue: #1e40af;
            --secondary-purple: #7c3aed;
            --accent-green: #10b981;
            --accent-red: #ef4444;
            --accent-yellow: #f59e0b;
            --accent-orange: #f97316;
            --accent-pink: #ec4899;
            --accent-cyan: #06b6d4;

            /* Neutral Colors */
            --text-primary: #ffffff;
            --text-secondary: #e5e7eb;
            --text-muted: #9ca3af;
            --text-dark: #1f2937;
            --text-light: #6b7280;

            /* Background Colors */
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --bg-light: #f8fafc;
            --bg-dark: #0a0f1c;
            --bg-glass: rgba(255, 255, 255, 0.1);
            --bg-glass-hover: rgba(255, 255, 255, 0.15);

            /* Border Colors */
            --border-primary: rgba(255, 255, 255, 0.1);
            --border-secondary: rgba(255, 255, 255, 0.2);
            --border-accent: rgba(59, 130, 246, 0.3);

            /* Shadow Colors */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);

            /* Spacing Scale */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;
            --space-3xl: 4rem;

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-full: 9999px;

            /* Typography */
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
            --font-size-4xl: 2.25rem;
            --font-size-5xl: 3rem;

            /* Transitions */
            --transition-fast: 0.15s ease-out;
            --transition-base: 0.3s ease-out;
            --transition-slow: 0.5s ease-out;

            /* Z-index Scale */
            --z-dropdown: 1000;
            --z-sticky: 1020;
            --z-fixed: 1030;
            --z-modal-backdrop: 1040;
            --z-modal: 1050;
            --z-popover: 1060;
            --z-tooltip: 1070;
            --z-toast: 1080;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        *::before,
        *::after {
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            color: var(--text-primary);
            line-height: 1.6;
            font-size: var(--font-size-base);
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* Enhanced Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 60%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 60%),
                radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.1) 0%, transparent 60%),
                radial-gradient(circle at 60% 70%, rgba(236, 72, 153, 0.08) 0%, transparent 50%);
            z-index: -2;
            animation: float 25s ease-in-out infinite;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.03) 50%, transparent 70%),
                linear-gradient(-45deg, transparent 30%, rgba(139, 92, 246, 0.03) 50%, transparent 70%);
            z-index: -1;
            animation: shimmer 30s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg) scale(1);
            }

            33% {
                transform: translateY(-15px) rotate(0.5deg) scale(1.02);
            }

            66% {
                transform: translateY(-10px) rotate(-0.5deg) scale(0.98);
            }
        }

        @keyframes shimmer {

            0%,
            100% {
                opacity: 0.5;
                transform: translateX(-100px);
            }

            50% {
                opacity: 0.8;
                transform: translateX(100px);
            }
        }

        /* Enhanced Navbar Styles */
        .navbar {
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            background: rgba(15, 23, 42, 0.8);
            border-bottom: 1px solid var(--border-primary);
            padding: var(--space-lg) 0;
            transition: all var(--transition-base);
            box-shadow: var(--shadow-lg);
            position: relative;
            z-index: var(--z-fixed);
        }

        .navbar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg,
                    rgba(59, 130, 246, 0.1) 0%,
                    rgba(139, 92, 246, 0.1) 50%,
                    rgba(59, 130, 246, 0.1) 100%);
            opacity: 0;
            transition: opacity var(--transition-base);
            z-index: -1;
        }

        .navbar:hover::before {
            opacity: 1;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: var(--font-size-2xl);
            color: var(--text-primary) !important;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: var(--space-md);
            transition: all var(--transition-base);
        }

        .navbar-brand:hover {
            transform: scale(1.05);
            color: var(--primary-blue) !important;
        }

        .navbar-nav .nav-link {
            color: var(--text-secondary) !important;
            font-weight: 500;
            margin: 0 var(--space-sm);
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--radius-lg);
            transition: all var(--transition-base);
            position: relative;
            overflow: hidden;
        }

        .navbar-nav .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
            transition: left var(--transition-slow);
        }

        .navbar-nav .nav-link:hover::before {
            left: 100%;
        }

        .navbar-nav .nav-link:hover {
            color: var(--text-primary) !important;
            background: var(--bg-glass-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .navbar-toggler {
            border: none;
            color: var(--text-primary);
            padding: var(--space-sm);
            border-radius: var(--radius-md);
            transition: all var(--transition-base);
        }

        .navbar-toggler:hover {
            background: var(--bg-glass);
            transform: scale(1.1);
        }

        .navbar-toggler:focus {
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        }

        /* Enhanced Logo Styles */
        .logo {
            width: 60px;
            height: 60px;
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-primary);
            font-weight: bold;
            font-size: var(--font-size-xl);
            box-shadow: var(--shadow-xl);
            transition: all var(--transition-base);
            position: relative;
            overflow: hidden;
        }

        .logo::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--primary-blue), var(--primary-purple), var(--accent-cyan));
            border-radius: var(--radius-xl);
            z-index: -1;
            opacity: 0;
            transition: opacity var(--transition-base);
        }

        .logo:hover::before {
            opacity: 1;
        }

        .logo:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: var(--shadow-glow);
        }

        .logo-img {
            height: 60px;
            width: auto;
            border-radius: var(--radius-lg);
            transition: all var(--transition-base);
            object-fit: cover;
        }

        .logo-img:hover {
            transform: scale(1.05);
        }

        /* Enhanced Main Content */
        main {
            min-height: calc(100vh - 200px);
            padding: var(--space-2xl) 0;
            position: relative;
            z-index: 1;
        }

        /* Enhanced Glass Card Styles */
        .glass-card {
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            background: var(--bg-glass);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-2xl);
            padding: var(--space-2xl);
            box-shadow: var(--shadow-xl);
            transition: all var(--transition-base);
            position: relative;
            overflow: hidden;
        }

        .glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg,
                    transparent,
                    rgba(59, 130, 246, 0.5),
                    rgba(139, 92, 246, 0.5),
                    transparent);
            opacity: 0;
            transition: opacity var(--transition-base);
        }

        .glass-card:hover::before {
            opacity: 1;
        }

        .glass-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-2xl);
            border-color: var(--border-accent);
            background: var(--bg-glass-hover);
        }

        /* Card Variants */
        .glass-card-sm {
            padding: var(--space-lg);
            border-radius: var(--radius-xl);
        }

        .glass-card-lg {
            padding: var(--space-3xl);
            border-radius: var(--radius-2xl);
        }

        /* Enhanced Button Styles */
        .btn-premium {
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
            border: none;
            color: var(--text-primary);
            padding: var(--space-md) var(--space-2xl);
            border-radius: var(--radius-xl);
            font-weight: 600;
            font-size: var(--font-size-base);
            transition: all var(--transition-base);
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-sm);
            cursor: pointer;
        }

        .btn-premium::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left var(--transition-slow);
        }

        .btn-premium::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--accent-cyan), var(--accent-pink));
            opacity: 0;
            transition: opacity var(--transition-base);
            z-index: -1;
        }

        .btn-premium:hover::before {
            left: 100%;
        }

        .btn-premium:hover::after {
            opacity: 1;
        }

        .btn-premium:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: var(--shadow-glow);
            color: var(--text-primary);
        }

        .btn-premium:active {
            transform: translateY(-1px) scale(1.02);
        }

        .btn-outline-light {
            border: 2px solid var(--border-secondary);
            color: var(--text-primary);
            background: transparent;
            padding: var(--space-md) var(--space-2xl);
            border-radius: var(--radius-xl);
            font-weight: 600;
            font-size: var(--font-size-base);
            transition: all var(--transition-base);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-sm);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .btn-outline-light::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-glass);
            opacity: 0;
            transition: opacity var(--transition-base);
            z-index: -1;
        }

        .btn-outline-light:hover::before {
            opacity: 1;
        }

        .btn-outline-light:hover {
            background: transparent;
            border-color: var(--primary-blue);
            color: var(--text-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        /* Enhanced Typography */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-md);
            line-height: 1.2;
            letter-spacing: -0.025em;
        }

        h1 {
            font-size: var(--font-size-5xl);
            background: linear-gradient(135deg, var(--text-primary), var(--primary-blue), var(--primary-purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: var(--space-lg);
        }

        h2 {
            font-size: var(--font-size-4xl);
            margin-bottom: var(--space-lg);
        }

        h3 {
            font-size: var(--font-size-3xl);
            margin-bottom: var(--space-md);
        }

        h4 {
            font-size: var(--font-size-2xl);
            margin-bottom: var(--space-md);
        }

        h5 {
            font-size: var(--font-size-xl);
            margin-bottom: var(--space-sm);
        }

        h6 {
            font-size: var(--font-size-lg);
            margin-bottom: var(--space-sm);
        }

        p {
            color: var(--text-secondary);
            line-height: 1.7;
            font-size: var(--font-size-lg);
            margin-bottom: var(--space-md);
        }

        .lead {
            font-size: var(--font-size-xl);
            font-weight: 400;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .text-muted {
            color: var(--text-muted) !important;
        }

        .text-primary {
            color: var(--primary-blue) !important;
        }

        .text-secondary {
            color: var(--text-secondary) !important;
        }

        /* Enhanced Animations */
        .fade-in-up {
            animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .fade-in {
            animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .slide-up {
            animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .slide-in-left {
            animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .slide-in-right {
            animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .scale-in {
            animation: scaleIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .bounce-in {
            animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-40px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(40px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }

            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes bounceIn {
            from {
                opacity: 0;
                transform: scale(0.3);
            }

            50% {
                opacity: 1;
                transform: scale(1.05);
            }

            70% {
                transform: scale(0.9);
            }

            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Enhanced Footer */
        .footer {
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border-top: 1px solid var(--border-primary);
            padding: var(--space-3xl) 0 var(--space-2xl);
            margin-top: auto;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg,
                    transparent,
                    var(--primary-blue),
                    var(--primary-purple),
                    transparent);
        }

        .footer-content {
            color: var(--text-secondary);
        }

        .footer h5 {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: var(--space-md);
        }

        .footer a {
            color: var(--primary-blue);
            text-decoration: none;
            transition: all var(--transition-base);
            position: relative;
        }

        .footer a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-blue), var(--primary-purple));
            transition: width var(--transition-base);
        }

        .footer a:hover::after {
            width: 100%;
        }

        .footer a:hover {
            color: var(--primary-purple);
            transform: translateY(-1px);
        }

        .social-links a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: var(--bg-glass);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-full);
            color: var(--text-secondary);
            transition: all var(--transition-base);
        }

        .social-links a:hover {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: var(--text-primary);
            transform: translateY(-2px) scale(1.1);
        }

        /* Enhanced Utility Classes */
        .text-success {
            color: var(--accent-green) !important;
        }

        .text-warning {
            color: var(--accent-yellow) !important;
        }

        .text-danger {
            color: var(--accent-red) !important;
        }

        .text-info {
            color: var(--accent-cyan) !important;
        }

        /* Enhanced Button Variants */
        .btn-success {
            background: linear-gradient(135deg, var(--accent-green), #34d399) !important;
            border: none !important;
            color: var(--text-primary) !important;
            box-shadow: var(--shadow-lg) !important;
            padding: var(--space-md) var(--space-2xl);
            border-radius: var(--radius-xl);
            font-weight: 600;
            transition: all var(--transition-base);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #059669, var(--accent-green)) !important;
            color: var(--text-primary) !important;
            transform: translateY(-3px) scale(1.05) !important;
            box-shadow: var(--shadow-glow) !important;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--accent-yellow), #fbbf24) !important;
            border: none !important;
            color: var(--text-dark) !important;
            box-shadow: var(--shadow-lg) !important;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--accent-red), #f87171) !important;
            border: none !important;
            color: var(--text-primary) !important;
            box-shadow: var(--shadow-lg) !important;
        }

        .btn-info {
            background: linear-gradient(135deg, var(--accent-cyan), #38bdf8) !important;
            border: none !important;
            color: var(--text-primary) !important;
            box-shadow: var(--shadow-lg) !important;
        }

        /* Loading and State Classes */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-blue);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1200px) {
            :root {
                --space-3xl: 3rem;
                --font-size-5xl: 2.5rem;
                --font-size-4xl: 2rem;
            }
        }

        @media (max-width: 992px) {
            .navbar-nav .nav-link {
                margin: var(--space-xs) 0;
                text-align: center;
            }

            .glass-card {
                padding: var(--space-xl);
            }
        }

        @media (max-width: 768px) {
            :root {
                --font-size-5xl: 2rem;
                --font-size-4xl: 1.75rem;
                --font-size-3xl: 1.5rem;
                --font-size-2xl: 1.25rem;
                --space-2xl: 2rem;
                --space-3xl: 2.5rem;
            }

            body {
                font-size: var(--font-size-sm);
            }

            .navbar {
                padding: var(--space-md) 0;
            }

            .navbar-brand {
                font-size: var(--font-size-xl);
            }

            .glass-card {
                padding: var(--space-lg);
                margin: var(--space-md);
                border-radius: var(--radius-xl);
            }

            .btn-premium,
            .btn-outline-light {
                padding: var(--space-sm) var(--space-lg);
                font-size: var(--font-size-sm);
            }

            main {
                padding: var(--space-xl) 0;
            }

            .footer {
                padding: var(--space-2xl) 0 var(--space-xl);
            }
        }

        @media (max-width: 576px) {
            :root {
                --font-size-5xl: 1.75rem;
                --font-size-4xl: 1.5rem;
                --font-size-3xl: 1.25rem;
                --space-xl: 1.25rem;
                --space-2xl: 1.5rem;
            }

            .glass-card {
                padding: var(--space-md);
                margin: var(--space-sm);
            }

            .navbar-brand {
                gap: var(--space-sm);
            }

            .logo,
            .logo-img {
                width: 50px;
                height: 50px;
            }
        }

        /* High DPI Displays */
        @media (-webkit-min-device-pixel-ratio: 2),
        (min-resolution: 192dpi) {
            body {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
        }

        /* Reduced Motion */
        @media (prefers-reduced-motion: reduce) {

            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Dark Mode Support */
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-light: var(--bg-dark);
            }
        }

        /* Enhanced Scroll Reveal Animation */
        .reveal {
            opacity: 0;
            transform: translateY(40px);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .reveal.active {
            opacity: 1;
            transform: translateY(0);
        }

        /* Intersection Observer Support */
        .reveal-left {
            opacity: 0;
            transform: translateX(-40px);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .reveal-right {
            opacity: 0;
            transform: translateX(40px);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .reveal-scale {
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .reveal-left.active,
        .reveal-right.active,
        .reveal-scale.active {
            opacity: 1;
            transform: translateX(0) scale(1);
        }

        /* Focus Styles */
        *:focus {
            outline: 2px solid var(--primary-blue);
            outline-offset: 2px;
        }

        .btn:focus,
        .form-control:focus,
        .form-select:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }

        /* Selection Styles */
        ::selection {
            background: var(--primary-blue);
            color: var(--text-primary);
        }

        ::-moz-selection {
            background: var(--primary-blue);
            color: var(--text-primary);
        }

        /* Scrollbar Styles */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-blue);
            border-radius: var(--radius-full);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-purple);
        }

        /* Performance Optimizations */
        .will-change-transform {
            will-change: transform;
        }

        .will-change-opacity {
            will-change: opacity;
        }

        .gpu-accelerated {
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
        }

        /* Additional Responsive Utilities */
        @media (max-width: 480px) {
            :root {
                --font-size-5xl: 1.5rem;
                --font-size-4xl: 1.25rem;
                --font-size-3xl: 1.125rem;
                --space-lg: 1rem;
                --space-xl: 1.25rem;
            }

            .glass-card {
                padding: var(--space-md);
                margin: var(--space-xs);
            }

            .btn-premium,
            .btn-outline-light {
                padding: var(--space-xs) var(--space-md);
                font-size: var(--font-size-xs);
            }
        }

        /* Print Styles */
        @media print {
            * {
                background: transparent !important;
                color: black !important;
                box-shadow: none !important;
                text-shadow: none !important;
            }

            .navbar,
            .footer,
            .btn,
            .floating-card {
                display: none !important;
            }

            .glass-card {
                border: 1px solid #ccc !important;
                background: white !important;
            }
        }

        /* Container Queries Support */
        @supports (container-type: inline-size) {
            .responsive-container {
                container-type: inline-size;
            }

            @container (max-width: 600px) {
                .container-responsive {
                    flex-direction: column;
                }
            }
        }

        /* Modern CSS Features */
        @supports (backdrop-filter: blur(10px)) {
            .glass-card {
                backdrop-filter: blur(30px);
                -webkit-backdrop-filter: blur(30px);
            }
        }

        @supports not (backdrop-filter: blur(10px)) {
            .glass-card {
                background: rgba(17, 24, 39, 0.95);
            }
        }

        /* Accessibility Improvements */
        @media (prefers-contrast: high) {
            :root {
                --text-primary: #ffffff;
                --text-secondary: #e5e7eb;
                --border-primary: #ffffff;
                --bg-glass: rgba(0, 0, 0, 0.9);
            }
        }

        /* Loading States */
        .loading-skeleton {
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%);
            background-size: 200% 100%;
            animation: loading-shimmer 2s infinite;
        }

        @keyframes loading-shimmer {
            0% {
                background-position: -200% 0;
            }

            100% {
                background-position: 200% 0;
            }
        }
    </style>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <!-- Club Logo -->
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <div class="logo">
                    <img src="{{ url_for('static', filename='images/itian.jpg') }}" alt="ITian Logo" class="logo-img">
                </div>
                <span>ITian Club</span>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <i class="fas fa-bars text-white"></i>
            </button>

            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    {% if session.get('user_email') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('instructions') }}">
                            <i class="fas fa-info-circle me-1"></i>Instructions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('quiz') }}">
                            <i class="fas fa-question-circle me-1"></i>Quiz
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dev') }}">
                            <i class="fas fa-code me-1"></i>Developer
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link logout-btn" href="#" title="Logout">
                            <i class="fas fa-sign-out-alt me-1"></i>Logout
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>

            <!-- College Logo -->
            <div class="logo ms-3 d-none d-lg-block">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="ITian Logo" class="logo-img">
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container" style="margin-top: 100px;">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="row">
                    <div class="col-md-4">
                        <h5 class="mb-3">ITian Club</h5>
                        <p class="mb-0">Empowering minds through technology and innovation.</p>
                    </div>
                    <div class="col-md-4">
                        <h5 class="mb-3">Contact</h5>
                        <p class="mb-1">
                            <i class="fas fa-envelope me-2"></i>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </p>
                        <p class="mb-0">
                            <i class="fas fa-phone me-2"></i>
                            <a href="tel:+1234567890">+1 (234) 567-890</a>
                        </p>
                    </div>
                    <div class="col-md-4">
                        <h5 class="mb-3">Follow Us</h5>
                        <div class="social-links">
                            <a href="#" class="me-3"><i class="fab fa-facebook"></i></a>
                            <a href="#" class="me-3"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="me-3"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="me-3"><i class="fab fa-linkedin"></i></a>
                        </div>
                    </div>
                </div>
                <hr class="my-4" style="border-color: rgba(255, 255, 255, 0.2);">
                <div class="text-center">
                    <p class="mb-0">&copy; 2024 ITian Club. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript Files -->
    <script src="{{ url_for('static', filename='flash_messages.js') }}"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script src="{{ url_for('static', filename='quiz_timer.js') }}"></script>

    <!-- Scroll Reveal Script -->
    <script>
        // Scroll reveal animation
        function reveal() {
            const reveals = document.querySelectorAll('.reveal');

            reveals.forEach(element => {
                const windowHeight = window.innerHeight;
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;

                if (elementTop < windowHeight - elementVisible) {
                    element.classList.add('active');
                }
            });
        }

        window.addEventListener('scroll', reveal);
        reveal(); // Initial call

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function () {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.15)';
                navbar.style.backdropFilter = 'blur(25px)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.1)';
                navbar.style.backdropFilter = 'blur(20px)';
            }
        });
    </script>
</body>

</html>
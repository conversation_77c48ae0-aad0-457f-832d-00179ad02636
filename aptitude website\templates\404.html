{% extends "base.html" %}
{% block title %}Page Not Found - ITian Club{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8 text-center">
        <div class="glass-card fade-in-up">
            <div class="error-icon mb-4">
                <i class="fas fa-exclamation-triangle fa-4x text-warning"></i>
            </div>
            <h1 class="display-4 fw-bold mb-3">404 - Page Not Found</h1>
            <p class="lead mb-4">Oops! The page you're looking for doesn't exist.</p>
            <p class="mb-4">The page may have been moved, deleted, or you entered the wrong URL.</p>
            
            <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                <a href="{{ url_for('index') }}" class="btn btn-premium btn-lg">
                    <i class="fas fa-home me-2"></i>
                    Go to Home
                </a>
                <button onclick="history.back()" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>
                    Go Back
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    .error-icon {
        animation: bounce 2s infinite;
    }
    
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }
</style>
{% endblock %}

